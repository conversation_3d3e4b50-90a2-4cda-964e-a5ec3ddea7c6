import AWS from "aws-sdk";
import { v4 as uuidv4 } from "uuid";

const s3 = new AWS.S3();

export const handler = async (event) => {
  try {
    // For Lambda Function URL or API Gateway, the body is a string
    const data = event.body;

    // Generate unique S3 object key
    const key = `webhook_uploads/${uuidv4()}.json`;

    // Put the object into your bucket
    await s3
      .putObject({
        Bucket: "your-s3-bucket",
        Key: key,
        Body: data,
        ContentType: "application/json",
      })
      .promise();

    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Success" }),
    };
  } catch (error) {
    console.error("Error uploading to S3:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Failed", error: error.message }),
    };
  }
};
